/**
 * FILE: PlatformChannelHandler.swift
 *
 * DESCRIPTION:
 *     Universal Platform Channel handler for Flutter-Swift communication.
 *     Provides VPN management interface equivalent to Go backend HTTP/WebSocket APIs.
 *     Shared implementation for both iOS and macOS platforms.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create universal Platform Channel handler
 */

import Foundation
import Flutter
import ItForceCore

/**
 * NAME: PlatformChannelHandler
 *
 * DESCRIPTION:
 *     Universal Platform Channel handler that bridges Flutter UI with Swift VPN services.
 *     Provides method channel for VPN operations and event channel for real-time updates.
 *     Equivalent to Go backend's HTTP API + WebSocket functionality.
 *
 *     DESIGN: This is a universal implementation shared between iOS and macOS.
 *     Platform-specific initialization is handled in respective AppDelegate files.
 *
 * PROPERTIES:
 *     methodChannel - Flutter method channel for VPN operations
 *     eventChannel - Flutter event channel for real-time updates
 *     vpnService - Core VPN service from ItForceCore
 *     eventSink - Flutter event sink for pushing updates
 *     logger - Universal logger
 */
public class PlatformChannelHandler: NSObject {
    
    // MARK: - Properties
    private var methodChannel: FlutterMethodChannel?
    private var eventChannel: FlutterEventChannel?
    private var eventSink: FlutterEventSink?

    // State deduplication (no time limit - only check if state is same)
    private var lastSentState: ConnectionState?
    private var lastStateChangeTime: Date = Date.distantPast

    // 临时注释掉锁以避免死锁问题
    // private let vpnServiceLock = NSLock()
    private var _vpnService: VPNService?
    private var vpnService: VPNService? {
        get {
            // vpnServiceLock.lock()
            // defer { vpnServiceLock.unlock() }
            return _vpnService
        }
        set {
            // vpnServiceLock.lock()
            // defer { vpnServiceLock.unlock() }
            _vpnService = newValue
        }
    }
    private let logger: LoggerProtocol



    // STEP 4: Event pushing optimization and control
    private var eventPushingEnabled: Bool = true
    private var lastTrafficEventTime: Date = Date.distantPast
    private var lastHeartbeatEventTime: Date = Date.distantPast
    private let trafficEventThrottle: TimeInterval = 1.0 // Minimum 1 second between traffic events
    private let heartbeatEventThrottle: TimeInterval = 2.0 // Minimum 2 seconds between heartbeat events



    // MARK: - Channel Names (matching Flutter Dart expectations)

    public static let methodChannelName = "panabit_client/methods"
    public static let eventChannelName = "panabit_client/events"

    // MARK: - Event Pushing Control (STEP 4)

    /**
     * NAME: setEventPushingEnabled
     *
     * DESCRIPTION:
     *     Controls whether event pushing is enabled or disabled.
     *     STEP 4: Added for event pushing control.
     *
     * PARAMETERS:
     *     enabled - Whether to enable event pushing
     */
    public func setEventPushingEnabled(_ enabled: Bool) {
        eventPushingEnabled = enabled
        logger.info("Event pushing \(enabled ? "enabled" : "disabled")")

        // Interface info timer logic removed - network interface info now included in status events
    }

    /**
     * NAME: isEventPushingEnabled
     *
     * DESCRIPTION:
     *     Returns whether event pushing is currently enabled.
     *     STEP 4: Added for event pushing status check.
     *
     * RETURNS:
     *     Bool - Whether event pushing is enabled
     */
    public func isEventPushingEnabled() -> Bool {
        return eventPushingEnabled
    }

    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes universal Platform Channel handler with Flutter engine.
     *
     * PARAMETERS:
     *     binaryMessenger - Flutter binary messenger
     */
    public override init() {
        // Create development logger with safe initialization
        do {
            self.logger = OSLogLogger.createDevelopmentLogger(category: "PlatformChannel")
        } catch {
            // Fallback to a simple logger if OSLog fails
            // print("⚠️ [PlatformChannelHandler] Failed to create OSLog logger, using fallback: \(error)") // Debug print commented for production
            self.logger = OSLogLogger.createDevelopmentLogger(category: "PlatformChannel")
        }

        super.init()

        // print("🔥🔥🔥 [PlatformChannelHandler] Default init() called") // Debug print commented for production
        // NSLog("🔥🔥🔥 [PlatformChannelHandler] Default init() called") // Debug NSLog commented for production

        // Safe logging with error handling
        do {
            logger.info("Platform Channel handler initialized (Flutter will be configured later)")
        } catch {
            // print("⚠️ [PlatformChannelHandler] Logger info failed: \(error)") // Debug print commented for production
        }
    }

    public init(binaryMessenger: FlutterBinaryMessenger) {
        // Create development logger for better debugging
        self.logger = OSLogLogger.createDevelopmentLogger(category: "PlatformChannel")

        // Setup channels
        self.methodChannel = FlutterMethodChannel(
            name: Self.methodChannelName,
            binaryMessenger: binaryMessenger
        )

        self.eventChannel = FlutterEventChannel(
            name: Self.eventChannelName,
            binaryMessenger: binaryMessenger
        )

        super.init()

        // Setup channel handlers
        setupChannelHandlers()

        logger.info("Universal Platform Channel handler initialized")
    }

    /**
     * NAME: configureFlutter
     *
     * DESCRIPTION:
     *     Configures Flutter channels at runtime when Flutter is available.
     *     This allows ItForceCore to work both with and without Flutter.
     *
     * PARAMETERS:
     *     binaryMessenger - Flutter binary messenger
     */
    public func configureFlutter(binaryMessenger: Any) {

        guard let messenger = binaryMessenger as? FlutterBinaryMessenger else {
            logger.error("Invalid binary messenger type")
            return
        }

        // Setup channels
        self.methodChannel = FlutterMethodChannel(
            name: Self.methodChannelName,
            binaryMessenger: messenger
        )

        self.eventChannel = FlutterEventChannel(
            name: Self.eventChannelName,
            binaryMessenger: messenger
        )

        // Setup channel handlers
        setupChannelHandlers()

        logger.info("Flutter channels configured at runtime")
    }
    
    // MARK: - Channel Setup
    
    /**
     * NAME: setupChannelHandlers
     *
     * DESCRIPTION:
     *     Sets up method and event channel handlers.
     */
    private func setupChannelHandlers() {
        // Setup method channel handler
        methodChannel?.setMethodCallHandler { [weak self] call, result in
            Task {
                await self?.handleMethodCall(call, result: result)
            }
        }

        // Setup event channel handler
        eventChannel?.setStreamHandler(self)

        // logger.debug("Platform Channel handlers configured") // Debug log commented for production
    }
    
    // MARK: - VPN Service Integration
    
    /**
     * NAME: initializeVPNService
     *
     * DESCRIPTION:
     *     Initializes VPN service with configuration.
     *     Equivalent to Go backend service initialization.
     */
    private func initializeVPNService() async throws {
        guard vpnService == nil else {
            // logger.debug("VPN service already initialized") // Debug log commented for production
            return
        }

        logger.info("Starting VPN service initialization...")

        do {
            // Create VPN service configuration (auto-reconnect handled by VPN Extension)
            let config = VPNServiceConfiguration(
                autoConnect: false,
                autoSelectBestServer: true,
                trafficUpdateInterval: 2.0
            )
            // logger.debug("VPN service configuration created") // Debug log commented for production

            // Create required services
            let logger = self.logger

            // Create server manager (ConnectionManager removed - handled by VPN Extension)
            // logger.debug("Creating ServerManager...") // Debug log commented for production
            let serverManager = ServerManager(logger: logger)

            // logger.debug("Creating ServerService...") // Debug log commented for production
            let serverService = ServerService(
                serverManager: serverManager,
                logger: logger
            )

            // Initialize VPN service with simplified architecture (ConnectionManager eliminated)
            // logger.debug("Creating VPNService with simplified architecture...") // Debug log commented for production
            vpnService = VPNService(
                serverService: serverService,
                configuration: config,
                logger: logger
            )

            // logger.debug("Setting VPN service delegate...") // Debug log commented for production
            await vpnService?.setDelegate(self)

            // Start VPN service
            // logger.debug("Starting VPN service...") // Debug log commented for production
            try await vpnService?.start()

            // Initialize server list from hardcoded URL (matching config.yaml)
            // logger.debug("Initializing server list...") // Debug log commented for production
            await initializeServerList()

            logger.info("VPN service initialized successfully")

            // Test delegate functionality after initialization
            logger.info("Starting automatic delegate test")
            Task {
                try? await Task.sleep(nanoseconds: 3_000_000_000) // Wait 3 seconds
                await testDelegateFunction()
            }

        } catch {
            logger.error("Failed to initialize VPN service", metadata: [
                "error": error.localizedDescription,
                "error_type": "\(type(of: error))"
            ])

            // Clean up partial initialization
            vpnService = nil
            throw error
        }
    }

    /**
     * Test delegate functionality
     */
    private func testDelegateFunction() async {
        logger.info("Testing delegate functionality")

        guard let vpnService = vpnService else {
            logger.error("VPN service is nil - cannot test")
            return
        }

        // Create test ping results
        let testResults: [String: PingResult] = [
            "1": PingResult(serverID: "1", latency: 100, isSuccess: true),
            "2": PingResult(serverID: "2", latency: 50, isSuccess: true),
            "3": PingResult(serverID: "3", latency: 200, isSuccess: true)
        ]

        logger.info("Calling VPNService ping test")
        await vpnService.testPingDelegate(with: testResults)
        logger.info("VPNService ping test completed")
    }

    /**
     * NAME: initializeServerList
     *
     * DESCRIPTION:
     *     Initializes server list from dynamic URL set by UI.
     *     No longer uses hardcoded URL - relies on UI lookup service.
     */
    private func initializeServerList() async {
        // No longer initialize with hardcoded URL
        // Server list URL will be set dynamically via setServerProviderUrl from UI
        logger.info("Server list initialization skipped - waiting for dynamic URL from UI")
    }

    /**
     * NAME: ServerListWithVersion
     *
     * DESCRIPTION:
     *     Structure containing server list and version information.
     *     Used for version comparison and UI update optimization.
     */
    private struct ServerListWithVersion {
        let servers: [ServerInfo]
        let version: String
    }

    /**
     * NAME: parseServerListJSON
     *
     * DESCRIPTION:
     *     Parses server list JSON response.
     *     Matches Go backend server list format.
     *     Returns server list with version information.
     *
     * PARAMETERS:
     *     data - JSON data to parse
     *
     * RETURNS:
     *     ServerListWithVersion - Parsed server list with version
     *
     * THROWS:
     *     Error - If parsing fails
     */
    private func parseServerListJSON(_ data: Data) throws -> ServerListWithVersion {
        // logger.debug("Parsing server list JSON", metadata: ["data_size": "\(data.count)"]) // Debug log commented for production

        let json = try JSONSerialization.jsonObject(with: data, options: [])

        guard let jsonDict = json as? [String: Any] else {
            logger.error("Invalid JSON format - not a dictionary")
            throw NSError(domain: "ParseError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON format"])
        }

        // logger.debug("JSON parsed successfully", metadata: ["keys": Array(jsonDict.keys).joined(separator: ", ")]) // Debug log commented for production

        // Extract version information
        let version: String
        if let versionString = jsonDict["version"] as? String {
            version = versionString
        } else {
            // Generate version based on content hash if not provided
            version = String(data.hashValue)
        }

        // Handle both formats: direct serverlist array or wrapped in response
        var serverListArray: [[String: Any]]

        if let serverlist = jsonDict["serverlist"] as? [[String: Any]] {
            // Format: {"version": "1.0", "serverlist": [...]}
            // logger.debug("Found serverlist array", metadata: ["count": "\(serverlist.count)"]) // Debug log commented for production
            serverListArray = serverlist
        } else if let data = jsonDict["data"] as? [[String: Any]] {
            // Format: {"success": true, "data": [...]}
            // logger.debug("Found data array", metadata: ["count": "\(data.count)"]) // Debug log commented for production
            serverListArray = data
        } else {
            logger.error("No serverlist or data array found", metadata: ["available_keys": Array(jsonDict.keys).joined(separator: ", ")])
            throw NSError(domain: "ParseError", code: 2, userInfo: [NSLocalizedDescriptionKey: "No serverlist or data array found"])
        }

        var servers: [ServerInfo] = []

        for (index, serverDict) in serverListArray.enumerated() {
            // logger.debug("Parsing server \(index)", metadata: [
            //     "server_keys": Array(serverDict.keys).joined(separator: ", ")
            // ]) // Debug log commented for production

            guard let id = serverDict["id"] as? Int,
                  let name = serverDict["name"] as? String,
                  let serverName = serverDict["serverName"] as? String,
                  let serverPort = serverDict["serverPort"] as? Int else {
                logger.warning("Skipping server \(index) due to missing required fields", metadata: [
                    "id": "\(serverDict["id"] ?? "nil")",
                    "name": "\(serverDict["name"] ?? "nil")",
                    "serverName": "\(serverDict["serverName"] ?? "nil")",
                    "serverPort": "\(serverDict["serverPort"] ?? "nil")"
                ])
                continue
            }

            let nameEn = serverDict["name_en"] as? String ?? name
            let isAuto = serverDict["isauto"] as? Bool ?? false

            let server = ServerInfo(
                id: String(id),
                name: name,
                nameEn: nameEn,
                serverName: serverName,
                serverPort: serverPort,
                isAuto: isAuto
            )

            // NSLog("🚨 CONN_FAIL_DEBUG: Parsed server - original_id=%d, string_id='%@', name='%@', isAuto=%@",
            //       id, server.id, server.name, server.isAuto ? "true" : "false") // Debug NSLog commented for production
            // print("🚨 CONN_FAIL_DEBUG: Parsed server - original_id=\(id), string_id='\(server.id)', name='\(server.name)', isAuto=\(server.isAuto)") // Debug print commented for production

            servers.append(server)
        }

        logger.info("Parsed server list response", metadata: [
            "version": version,
            "server_count": "\(servers.count)"
        ])

        return ServerListWithVersion(servers: servers, version: version)
    }

    // MARK: - Method Call Handling
    
    /**
     * NAME: handleMethodCall
     *
     * DESCRIPTION:
     *     Handles Flutter method calls, equivalent to Go backend HTTP API endpoints.
     *
     * PARAMETERS:
     *     call - Flutter method call
     *     result - Flutter result callback
     */
    private func handleMethodCall(_ call: FlutterMethodCall, result: @escaping FlutterResult) async {
        // logger.debug("Handling method call", metadata: ["method": call.method]) // Debug log commented for production
        
        do {
            // Ensure VPN service is initialized
            try await initializeVPNService()
            
            // Handle method calls (equivalent to Go HTTP API endpoints)
            switch call.method {
            case "initializeBackend":
                await handleInitializeBackend(result: result)
            case "login":
                await handleLogin(call.arguments, result: result)
            case "connect":
                // NSLog("🚨 CONN_FAIL_DEBUG: Method channel received 'connect' call") // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: Method channel received 'connect' call") // Debug print commented for production
                await handleConnect(call.arguments, result: result)
            case "disconnect":
                await handleDisconnect(result: result)

            case "getStatus":
                await handleGetStatus(result: result)
            case "getServers":
                await handleGetServers(result: result)
            case "pingServer":
                await handlePingServer(call.arguments, result: result)
            case "pingServers":
                await handlePingServers(result: result)
            case "getInterface":
                await handleGetInterface(result: result)
            case "getRoutingSettings":
                await handleGetRoutingSettings(result: result)
            case "setRoutingSettings":
                await handleSetRoutingSettings(call.arguments, result: result)
            case "setServerProviderUrl":
                await handleSetServerProviderUrl(call.arguments, result: result)
            case "health":
                await handleHealthCheck(result: result)
            case "checkVPNPermission":
                // iOS平台不需要显式权限检查，权限在连接时自动处理
                await handleCheckVPNPermission(result: result)
            case "requestVPNPermission":
                // iOS平台不需要显式权限请求，权限在连接时自动处理
                await handleRequestVPNPermission(call.arguments, result: result)
            case "loadVPNConfiguration":
                await handleLoadVPNConfiguration(result: result)
            case "getVPNStatus":
                await handleGetVPNStatus(result: result)
            default:
                result(FlutterMethodNotImplemented)
            }
        } catch {
            logger.error("Method call failed", metadata: [
                "method": call.method,
                "error": error.localizedDescription
            ])
            result(FlutterError(
                code: "INITIALIZATION_FAILED",
                message: "Failed to initialize VPN service: \(error.localizedDescription)",
                details: nil
            ))
        }
    }
    
    // MARK: - API Method Implementations (Go HTTP API equivalents)

    /**
     * NAME: handleInitializeBackend
     *
     * DESCRIPTION:
     *     Handles backend initialization request from Flutter.
     *     Initializes core system without creating separate BackendChannelHandler.
     *     This simplifies the architecture and avoids circular dependencies.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleInitializeBackend(result: @escaping FlutterResult) async {
        logger.info("Handling backend initialization request from Flutter")

        do {
            // Initialize core system if not already done
            if !ItForceCoreManager.shared.isInitialized {
                logger.info("Initializing ItForceCoreManager...")
                try ItForceCoreManager.shared.initialize()
                logger.info("ItForceCoreManager initialized successfully")
            } else {
                logger.info("ItForceCoreManager already initialized")
            }

            logger.info("Backend initialization completed successfully")
            result(true)

        } catch {
            logger.error("Backend initialization failed", metadata: ["error": error.localizedDescription])
            result(FlutterError(
                code: "BACKEND_INIT_FAILED",
                message: "Failed to initialize backend: \(error.localizedDescription)",
                details: nil
            ))
        }
    }

    /**
     * NAME: handleInitializeBackendFromAppDelegate
     *
     * DESCRIPTION:
     *     Handles backend initialization called from AppDelegate.
     *     This is called after PlatformChannelHandler is created.
     *     This method is always available regardless of Flutter compilation.
     *
     * PARAMETERS:
     *     result - Flutter result callback (Any type to avoid Flutter dependency)
     */
    public func handleInitializeBackendFromAppDelegate(result: Any) async {
        if let flutterResult = result as? FlutterResult {
            await handleInitializeBackend(result: flutterResult)
        } else {
            logger.error("Invalid result type - expected FlutterResult")
        }
    }

    /**
     * NAME: handleLogin
     *
     * DESCRIPTION:
     *     Handles user login, equivalent to POST /api/login.
     *     Performs authentication and returns best server information.
     *
     * PARAMETERS:
     *     arguments - Login arguments (username, password)
     *     result - Flutter result callback
     */
    private func handleLogin(_ arguments: Any?, result: @escaping FlutterResult) async {
        guard let args = arguments as? [String: Any],
              let username = args["username"] as? String,
              let password = args["password"] as? String else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing username or password",
                details: nil
            ))
            return
        }

        logger.info("Processing login request", metadata: ["username": username])

        // Log received password for debugging (iOS/macOS receives plaintext password)
        logger.info("Received password from Flutter", metadata: [
            "password": password,
            "password_length": "\(password.count)"
        ])

        // Note: Network connectivity check removed - handled by VPN Extension

        do {
            // Initialize VPN service if not already done
            if vpnService == nil {
                logger.info("Initializing VPN service for login")
                try await initializeVPNService()
            }

            // Get server list for authentication (equivalent to Go backend login flow)
            logger.info("Getting server list for authentication")
            let servers = try await vpnService?.serverService.getServerList() ?? []
            logger.info("Retrieved server list", metadata: ["server_count": "\(servers.count)"])

            // Execute ping operation to get latest latency data before server selection
            logger.info("Starting ping operation for all servers before authentication")

            // Send ping start event to notify Flutter UI
            sendEvent(type: "ping_start", data: [:])

            let pingStartTime = Date()
            try await vpnService?.serverService.pingAllServers()
            let pingEndTime = Date()
            let pingDuration = pingEndTime.timeIntervalSince(pingStartTime)

            logger.info("Ping operation completed for login", metadata: [
                "duration": "\(pingDuration)",
                "server_count": "\(servers.count)"
            ])

            // Get updated server list with fresh ping data (bypass cache to ensure latest ping results)
            logger.info("Getting updated server list with fresh ping data")
            let updatedServers = try await vpnService?.serverService.getFreshServerList() ?? []
            logger.info("Retrieved updated server list", metadata: ["server_count": "\(updatedServers.count)"])

            // Log some ping results for verification
            let serversWithPing = updatedServers.filter { $0.ping > 0 }
            logger.info("Login ping verification: \(serversWithPing.count)/\(updatedServers.count) servers have valid ping")
            for server in serversWithPing.prefix(3) {
                // logger.debug("Login server ping: \(server.name) -> \(server.ping)ms") // Debug log commented for production
            }

            // Use proper server selection logic matching Go backend SelectBestAutoServer()
            guard let bestServer = selectBestAutoServer(from: updatedServers) else {
                logger.error("No suitable auto server found after ping operation")
                result(FlutterError(
                    code: "NO_AUTO_SERVER",
                    message: "No suitable automatic server found",
                    details: nil
                ))
                return
            }

            // Perform actual authentication with the server (matching Go backend flow)
            logger.info("Starting authentication with selected server", metadata: [
                "username": username,
                "server": bestServer.serverName,
                "port": "\(bestServer.serverPort)",
                "server_id": bestServer.id
            ])

            // Resolve server IP using ServerManager's IP cache before authentication
            logger.info("Resolving server IP using ServerManager cache")
            let serverAddress = await vpnService?.serverService.serverManager.resolveServerIP(bestServer.serverName) ?? bestServer.serverName
            let useResolvedIP = serverAddress != bestServer.serverName

            logger.info("Using server address for authentication", metadata: [
                "hostname": bestServer.serverName,
                "resolved_address": serverAddress,
                "from_cache": useResolvedIP ? "true" : "false",
                "use_resolved_ip": useResolvedIP ? "true" : "false",
                "fallback_note": useResolvedIP ? "Using resolved IP" : "Using hostname fallback - DNS resolution will occur in network layer"
            ])

            logger.info("Creating AuthenticationManager instance")
            let authManager = AuthenticationManager()

            logger.info("Calling authenticate method with parameters", metadata: [
                "server_address": serverAddress,
                "server_port": "\(bestServer.serverPort)",
                "mtu": "1500",
                "encryption_method": "AES256"
            ])

            let authResult = try await authManager.authenticate(
                username: username,
                password: password,
                serverAddress: serverAddress, // Use resolved IP address
                serverPort: bestServer.serverPort,
                mtu: 1400, // Match Windows MTU
                encryptionMethod: .xor // Match Windows encryption method (0x01)
            )

            // Check authentication result
            guard authResult.success else {
                let errorMessage = authResult.errorMessage ?? "Authentication failed"
                // Map reject reason to proper error code
                // Use specific error codes for known cases, default to authInvalidCredentials
                let errorCode: Int
                if let rejectReason = authResult.rejectReason {
                    switch rejectReason {
                    case 1: // Invalid username
                        errorCode = ErrorCode.authInvalidCredentials.rawValue // 2000
                    case 2: // Invalid password
                        errorCode = ErrorCode.authInvalidCredentials.rawValue // 2000
                    case 7: // Account disabled
                        errorCode = ErrorCode.authAccountLocked.rawValue // 2003
                    case 8: // Maximum sessions reached
                        errorCode = ErrorCode.authRateLimited.rawValue // 2002
                    default: // Unknown reject reason - use authInvalidCredentials instead of networkTimeout
                        errorCode = ErrorCode.authInvalidCredentials.rawValue // 2000
                    }
                } else {
                    // No reject reason - use authInvalidCredentials instead of networkTimeout
                    errorCode = ErrorCode.authInvalidCredentials.rawValue // 2000
                }

                logger.error("Authentication failed", metadata: [
                    "username": username,
                    "server": bestServer.serverName,
                    "error": errorMessage,
                    "reject_reason": authResult.rejectReason != nil ? String(authResult.rejectReason!) : "nil",
                    "mapped_error_code": String(errorCode)
                ])

                result(FlutterError(
                    code: String(errorCode), // Use string code to match Flutter expectations
                    message: errorMessage,
                    details: [
                        "error_code": errorCode,
                        "server": bestServer.serverName
                    ]
                ))
                return
            }

            // Save credentials to VPN service for future VPN connections
            logger.info("Saving user credentials to VPN service for future connections")
            try await vpnService?.saveUserCredentials(username: username, password: password)
            logger.info("User credentials saved successfully")

            // Prepare response (matching Go API response format)
            let response: [String: Any] = [
                "success": true,
                "message": "Login successful",
                "data": [
                    "best_server": bestServer.toDictionary()
                ]
            ]

            // Send ping_results event to update Flutter UI with latest latency data
            logger.info("Sending ping_results event after successful login")
            let serverDicts = updatedServers.map { $0.toDictionary() }
            sendEvent(type: "ping_results", data: ["servers": serverDicts])
            logger.info("Sent ping_results event with \(serverDicts.count) servers")

            result(response)
            logger.info("Login successful", metadata: [
                "username": username,
                "server": bestServer.serverName,
                "session_id": authResult.sessionID != nil ? String(format: "0x%04X", authResult.sessionID!) : "none"
            ])
            
        } catch let error as VPNServiceError {
            let flutterError = convertVPNErrorToFlutter(error)
            result(flutterError)
            logger.error("Login failed", metadata: [
                "username": username,
                "error": error.localizedDescription
            ])
        } catch ProtocolError.timeout {
            // Handle authentication timeout specifically
            result(FlutterError(
                code: "AUTHENTICATION_TIMEOUT",
                message: "Authentication timed out. Please check your network connection and try again.",
                details: [
                    "error_code": ErrorCode.networkTimeout.rawValue,
                    "timeout_duration": 10.0,
                    "retry_count": 5
                ]
            ))
            logger.error("Login failed due to timeout", metadata: [
                "username": username,
                "timeout_duration": "10.0",
                "retry_count": "5"
            ])
        } catch ProtocolError.networkError(let errorMessage) {
            // Handle authentication network error specifically
            result(FlutterError(
                code: "NETWORK_ERROR",
                message: "Network connection failed. Please check your network connection and try again.",
                details: [
                    "error_code": ErrorCode.networkUnreachable.rawValue,
                    "network_error": errorMessage,
                    "retry_count": 3
                ]
            ))
            logger.error("Login failed due to network error", metadata: [
                "username": username,
                "network_error": errorMessage,
                "retry_count": "3"
            ])
        } catch {
            result(FlutterError(
                code: "AUTH_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Login failed with unexpected error", metadata: [
                "username": username,
                "error": error.localizedDescription
            ])
        }
    }
    
    /**
     * NAME: handleConnect
     *
     * DESCRIPTION:
     *     Handles VPN connection request, equivalent to POST /api/connect.
     *     Starts VPN connection to specified server.
     *
     * PARAMETERS:
     *     arguments - Connect arguments (server_id)
     *     result - Flutter result callback
     */
    private func handleConnect(_ arguments: Any?, result: @escaping FlutterResult) async {
        // NSLog("🚨 CONN_FAIL_DEBUG: handleConnect called with arguments: %@", String(describing: arguments)) // Debug NSLog commented for production
        // debugPrint("🚨 CONN_FAIL_DEBUG: handleConnect called with arguments: \(String(describing: arguments))") // Debug print commented for production

        guard let args = arguments as? [String: Any],
              let serverID = args["server_id"] as? String else {
            // NSLog("🚨 CONN_FAIL_DEBUG: Invalid arguments - missing server_id") // Debug NSLog commented for production
            // print("🚨 CONN_FAIL_DEBUG: Invalid arguments - missing server_id") // Debug print commented for production
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing server_id",
                details: nil
            ))
            return
        }

        // NSLog("🚨 CONN_FAIL_DEBUG: Processing connect request, server_id: %@", serverID) // Debug NSLog commented for production
        // print("🚨 CONN_FAIL_DEBUG: Processing connect request, server_id: \(serverID)") // Debug print commented for production

        // DEBUG: Log VPN service state before connection attempt
        if let vpnService = vpnService {
            let currentState = await vpnService.getCurrentVPNState()
            // logger.info("🔍 [DEBUG] VPN service state before connect", metadata: [
            //     "server_id": serverID,
            //     "vpn_service_exists": "true",
            //     "current_state": "\(currentState)",
            //     "thread": Thread.isMainThread ? "main" : "background"
            // ]) // Debug log commented for production
        } else {
            // logger.error("🚫 [DEBUG] VPN service is nil", metadata: [
            //     "server_id": serverID
            // ]) // Debug log commented for production
        }

        do {
            // CRITICAL FIX: Use connect method only, as UI layer expects
            // UI layer handles server switching via disconnect + connect pattern
            // logger.info("🚀 [DEBUG] Processing connect request", metadata: [
            //     "server_id": serverID,
            //     "note": "Using connect() method - UI handles server switching via disconnect+connect"
            // ]) // Debug log commented for production

            if let vpnService = vpnService {
                // NSLog("🚨 CONN_FAIL_DEBUG: About to select server and connect, server_id: %@", serverID) // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: About to select server and connect, server_id: \(serverID)") // Debug print commented for production

                // CRITICAL DEBUG: Check server list before selection
                do {
                    let serverList = try await vpnService.serverService.getServerList()
                    // logger.info("TRACE: Current server list has \(serverList.count) servers") // Debug log commented for production

                    let targetServer = serverList.first { $0.id == serverID }
                    if let server = targetServer {
                        // logger.info("TRACE: Target server found", metadata: [
                        //     "server_id": server.id,
                        //     "server_name": server.name,
                        //     "status": server.status.rawValue,
                        //     "isAuto": "\(server.isAuto)"
                        // ]) // Debug log commented for production
                    } else {
                        // logger.error("TRACE: Target server NOT found in server list", metadata: [
                        //     "requested_server_id": serverID,
                        //     "available_servers": serverList.map { "\($0.id):\($0.name)" }.joined(separator: ", ")
                        // ]) // Debug log commented for production
                    }
                } catch {
                    // logger.error("TRACE: Failed to get server list", metadata: ["error": error.localizedDescription]) // Debug log commented for production
                }

                // CRITICAL DEBUG: Check server list before selection
                do {
                    let serverList = try await vpnService.serverService.getServerList()
                    // NSLog("🚨 CONN_FAIL_DEBUG: Current server list has %d servers", serverList.count) // Debug NSLog commented for production
                    // print("🚨 CONN_FAIL_DEBUG: Current server list has \(serverList.count) servers") // Debug print commented for production

                    for (index, server) in serverList.enumerated() {
                        // NSLog("🚨 CONN_FAIL_DEBUG: Available server[%d]: id='%@', name='%@', isAuto=%@", index, server.id, server.name, server.isAuto ? "true" : "false") // Debug NSLog commented for production
                    }

                    let targetServer = serverList.first { $0.id == serverID }
                    if let server = targetServer {
                        // NSLog("🚨 CONN_FAIL_DEBUG: Target server FOUND - requested_id='%@', found_id='%@', server_name='%@', status='%@', isAuto=%@",
                        //       serverID, server.id, server.name, server.status.rawValue, server.isAuto ? "true" : "false") // Debug NSLog commented for production
                        // print("🚨 CONN_FAIL_DEBUG: Target server FOUND - requested_id='\(serverID)', found_id='\(server.id)', server_name='\(server.name)', status='\(server.status.rawValue)', isAuto=\(server.isAuto)") // Debug print commented for production
                    } else {
                        // NSLog("🚨 CONN_FAIL_DEBUG: Target server NOT FOUND - requested_id='%@', available_ids=[%@]",
                        //       serverID, serverList.map { "'\($0.id)'" }.joined(separator: ", ")) // Debug NSLog commented for production
                        // print("🚨 CONN_FAIL_DEBUG: Target server NOT FOUND - requested_id='\(serverID)', available_ids=[\(serverList.map { "'\($0.id)'" }.joined(separator: ", "))]") // Debug print commented for production
                    }
                } catch {
                    // NSLog("🚨 CONN_FAIL_DEBUG: Failed to get server list - error: %@", error.localizedDescription) // Debug NSLog commented for production
                    // print("🚨 CONN_FAIL_DEBUG: Failed to get server list - error: \(error.localizedDescription)") // Debug print commented for production
                }

                // CRITICAL FIX: Select the specified server before connecting
                // The UI layer passes serverID expecting us to connect to that specific server
                try await vpnService.serverService.selectServer(serverID: serverID)
                // NSLog("🚨 CONN_FAIL_DEBUG: Server selected successfully, server_id: %@", serverID) // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: Server selected successfully, server_id: \(serverID)") // Debug print commented for production

                // Now connect to the selected server
                try await vpnService.connect()
                // NSLog("🚨 CONN_FAIL_DEBUG: vpnService.connect() completed successfully") // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: vpnService.connect() completed successfully") // Debug print commented for production
            } else {
                // NSLog("🚨 CONN_FAIL_DEBUG: VPN service is nil, throwing serviceNotStarted") // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: VPN service is nil, throwing serviceNotStarted") // Debug print commented for production
                throw VPNServiceError.serviceNotStarted
            }

            // Return simple boolean true (consistent with other platforms)
            // NSLog("🚨 CONN_FAIL_DEBUG: Returning true to Flutter") // Debug NSLog commented for production
            // print("🚨 CONN_FAIL_DEBUG: Returning true to Flutter") // Debug print commented for production
            result(true)
            logger.info("VPN connection started", metadata: ["server_id": serverID])
            
        } catch let error as VPNServiceError {
            // NSLog("🚨 CONN_FAIL_DEBUG: VPNServiceError caught - server_id: %@, error: %@", serverID, error.localizedDescription) // Debug NSLog commented for production
            // print("🚨 CONN_FAIL_DEBUG: VPNServiceError caught - server_id: \(serverID), error: \(error.localizedDescription)") // Debug print commented for production
            let flutterError = convertVPNErrorToFlutter(error)
            result(flutterError)
            logger.error("Connect failed", metadata: [
                "server_id": serverID,
                "error": error.localizedDescription
            ])
        } catch {
            // NSLog("🚨 CONN_FAIL_DEBUG: Unexpected error caught - server_id: %@, error: %@", serverID, error.localizedDescription) // Debug NSLog commented for production
            // print("🚨 CONN_FAIL_DEBUG: Unexpected error caught - server_id: \(serverID), error: \(error.localizedDescription)") // Debug print commented for production
            result(FlutterError(
                code: "CONNECTION_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Connect failed with unexpected error", metadata: [
                "server_id": serverID,
                "error": error.localizedDescription
            ])
        }
    }

    /**
     * NAME: handleDisconnect
     *
     * DESCRIPTION:
     *     Handles VPN disconnection request, equivalent to POST /api/disconnect.
     *     Waits for complete disconnection before returning response to ensure proper timing.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleDisconnect(result: @escaping FlutterResult) async {
        logger.info("Processing disconnect request")

        do {
            // Disconnect VPN and wait for completion (equivalent to Go backend disconnect flow)
            try await vpnService?.disconnect()

            // Return response only after disconnection is complete
            let response: [String: Any] = [
                "success": true,
                "message": "VPN disconnection completed"
            ]

            result(response)
            logger.info("VPN disconnection completed successfully")

        } catch let error as VPNServiceError {
            let flutterError = convertVPNErrorToFlutter(error)
            result(flutterError)
            logger.error("Disconnect failed", metadata: ["error": error.localizedDescription])
        } catch {
            result(FlutterError(
                code: "DISCONNECTION_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Disconnect failed with unexpected error", metadata: [
                "error": error.localizedDescription
            ])
        }
    }



    /**
     * NAME: handleGetStatus
     *
     * DESCRIPTION:
     *     Handles status query request, equivalent to GET /api/status.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleGetStatus(result: @escaping FlutterResult) async {
        // logger.debug("Processing get status request") // Debug log commented for production

        do {
            // Get current VPN status (equivalent to Go backend status query)
            let status = try await vpnService?.getConnectionInfo()

            // Convert to response format (matching Go API response)
            let response: [String: Any] = [
                "success": true,
                "data": status?.toDictionary() ?? [:]
            ]

            result(response)
            // logger.debug("Status query completed") // Debug log commented for production

        } catch {
            result(FlutterError(
                code: "STATUS_QUERY_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Status query failed", metadata: ["error": error.localizedDescription])
        }
    }

    /**
     * NAME: handleGetServers
     *
     * DESCRIPTION:
     *     Handles server list query, equivalent to GET /api/servers.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleGetServers(result: @escaping FlutterResult) async {
        // logger.debug("Processing get servers request") // Debug log commented for production

        do {
            // Get server list (equivalent to Go backend server query)
            let servers = try await vpnService?.serverService.getServerList() ?? []

            // Convert to response format
            let serverDicts = servers.map { $0.toDictionary() }
            let response: [String: Any] = [
                "success": true,
                "data": serverDicts
            ]

            result(response)
            logger.debug("Server list query completed", metadata: ["server_count": "\(servers.count)"])

        } catch {
            result(FlutterError(
                code: "SERVER_QUERY_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Server query failed", metadata: ["error": error.localizedDescription])
        }
    }

    /**
     * NAME: handlePingServers
     *
     * DESCRIPTION:
     *     Handles server ping request, equivalent to POST /api/servers/ping.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handlePingServers(result: @escaping FlutterResult) async {
        logger.info("Processing ping servers request")

        guard let vpnService = vpnService else {
            logger.error("VPN service not initialized for ping operation")
            result(FlutterError(
                code: "SERVICE_NOT_INITIALIZED",
                message: "VPN service not initialized",
                details: nil
            ))
            return
        }
        logger.info("VPN service is available for ping operation")

        do {
            // Check if we have servers before pinging
            logger.info("Checking server list before ping")
            let servers = try await vpnService.serverService.getServerList()
            logger.info("Current server count", metadata: ["count": "\(servers.count)"])

            // Log first few servers for debugging
            for (index, server) in servers.prefix(3).enumerated() {
                // logger.debug("Server[\(index)]: \(server.name) - \(server.serverName):\(server.serverPort)") // Debug log commented for production
            }

            if servers.isEmpty {
                logger.warning("No servers available for ping, initializing server list first")
                await initializeServerList()

                // Check again after initialization
                let updatedServers = try await vpnService.serverService.getServerList()
                logger.info("Server count after initialization", metadata: ["count": "\(updatedServers.count)"])

                if updatedServers.isEmpty {
                    logger.error("Still no servers available after initialization")
                    result(FlutterError(
                        code: "NO_SERVERS",
                        message: "No servers available for ping",
                        details: nil
                    ))
                    return
                }
            }

            // Send ping start event (equivalent to Go WebSocket ping_start event)
            logger.info("Sending ping_start event")
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - START - thread: \(Thread.current)")
            print("🔍 [PING_DEBUG] Sending ping_start event to Flutter")
            sendEvent(type: "ping_start", data: [:])
            print("🔍 [PING_DEBUG] ping_start event sent successfully")

            // Start server ping operation (equivalent to Go backend ping)
            logger.info("Starting server ping operation")
            print("🔍 [PING_DEBUG] About to call vpnService.serverService.pingAllServers() - time: \(Date())")

            let startTime = Date()
            try await vpnService.serverService.pingAllServers()
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            logger.info("Server ping operation completed successfully", metadata: ["duration": "\(duration) seconds"])
            print("🔍 [PING_DEBUG] vpnService.serverService.pingAllServers() completed - duration: \(duration)s")

            // Return immediate response (Go backend pattern)
            let response: [String: Any] = [
                "success": true,
                "message": "Server ping started"
            ]

            print("🔍 [PING_DEBUG] About to send success response to Flutter")
            result(response)
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - SUCCESS RESPONSE SENT")

        } catch let error as VPNServiceError {
            logger.error("Ping servers failed with VPNServiceError: \(error.localizedDescription)")
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - VPNServiceError: \(error.localizedDescription)")
            let flutterError = convertVPNErrorToFlutter(error)
            result(flutterError)
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - ERROR RESPONSE SENT (VPNServiceError)")
        } catch {
            logger.error("Ping servers failed with unexpected error: \(error.localizedDescription)")
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - Unexpected error: \(error.localizedDescription)")
            print("🔍 [PING_DEBUG] Error type: \(type(of: error))")
            result(FlutterError(
                code: "PING_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            print("🔍 [PING_DEBUG] PlatformChannelHandler.pingServers() - ERROR RESPONSE SENT (Unexpected)")
        }
    }

    /**
     * NAME: handlePingServer
     *
     * DESCRIPTION:
     *     Handles single server ping request, equivalent to POST /api/servers/ping.
     *
     * PARAMETERS:
     *     arguments - Ping arguments (server_id)
     *     result - Flutter result callback
     */
    private func handlePingServer(_ arguments: Any?, result: @escaping FlutterResult) async {
        guard let args = arguments as? [String: Any],
              let serverID = args["server_id"] as? String else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing server_id",
                details: nil
            ))
            return
        }

        logger.debug("Processing ping server request", metadata: ["server_id": serverID])

        do {
            // Get server list to find the target server
            let servers = try await vpnService?.serverService.getServerList() ?? []
            guard let targetServer = servers.first(where: { $0.id == serverID }) else {
                result(-1) // Server not found, return -1
                return
            }

            // Ping the specific server (this will be handled by ServerManager's ping functionality)
            // For now, we'll return the current ping value from the server list
            // The actual ping operation is handled by the background ping task
            let pingValue = targetServer.ping > 0 ? targetServer.ping : -1

            result(pingValue)
            logger.debug("Server ping completed", metadata: [
                "server_id": serverID,
                "ping": "\(pingValue)"
            ])

        } catch let error as VPNServiceError {
            logger.error("Ping server failed", metadata: [
                "server_id": serverID,
                "error": error.localizedDescription
            ])
            result(-1) // Return -1 for errors
        } catch {
            logger.error("Ping server failed with unexpected error", metadata: [
                "server_id": serverID,
                "error": error.localizedDescription
            ])
            result(-1) // Return -1 for errors
        }
    }

    /**
     * NAME: handleGetInterface
     *
     * DESCRIPTION:
     *     Handles interface info query, equivalent to GET /api/interface.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleGetInterface(result: @escaping FlutterResult) async {
        logger.debug("Processing get interface request")

        do {
            // Get network interface information (simplified for iOS/macOS)
            let connectionInfo = try await vpnService?.getConnectionInfo()
            let interfaceInfo: [String: Any] = [
                "state": connectionInfo?.state.rawValue ?? 0,
                "connected_server": connectionInfo?.connectedServer?.toDictionary() ?? [:],
                "traffic": connectionInfo?.trafficStats.toDictionary() ?? [:]
            ]

            // Convert to response format
            let response: [String: Any] = [
                "success": true,
                "data": interfaceInfo
            ]

            result(response)
            logger.debug("Interface info query completed")

        } catch {
            result(FlutterError(
                code: "INTERFACE_QUERY_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Interface query failed", metadata: ["error": error.localizedDescription])
        }
    }

    /**
     * NAME: handleGetRoutingSettings
     *
     * DESCRIPTION:
     *     Handles routing settings query, equivalent to GET /api/settings/routing.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleGetRoutingSettings(result: @escaping FlutterResult) async {
        logger.debug("Processing get routing settings request")

        // Get stored routing settings
        let storedSettings = await getStoredRoutingSettings()

        let routingSettings: [String: Any] = [
            "mode": storedSettings.mode,
            "custom_routes": storedSettings.customRoutes
        ]

        let response: [String: Any] = [
            "success": true,
            "data": routingSettings
        ]

        result(response)
        logger.debug("Routing settings query completed")
    }

    /**
     * NAME: RoutingSettings
     *
     * DESCRIPTION:
     *     Structure to hold routing configuration.
     */
    private struct RoutingSettings {
        let mode: String
        let customRoutes: String

        init(mode: String = "all", customRoutes: String = "") {
            self.mode = mode
            self.customRoutes = customRoutes
        }
    }

    /**
     * NAME: storeRoutingSettings
     *
     * DESCRIPTION:
     *     Stores routing settings for use during VPN connection.
     *
     * PARAMETERS:
     *     mode - Routing mode ("all" or "custom")
     *     customRoutes - Custom routes string (comma-separated CIDR)
     */
    private func storeRoutingSettings(mode: String, customRoutes: String) async {
        let userDefaults = UserDefaults.standard
        userDefaults.set(mode, forKey: "vpn_routing_mode")
        userDefaults.set(customRoutes, forKey: "vpn_custom_routes")
        userDefaults.synchronize()

        logger.info("Routing settings stored", metadata: [
            "mode": mode,
            "custom_routes": customRoutes
        ])
    }

    /**
     * NAME: getStoredRoutingSettings
     *
     * DESCRIPTION:
     *     Retrieves stored routing settings.
     *
     * RETURNS:
     *     RoutingSettings - Current routing configuration
     */
    private func getStoredRoutingSettings() async -> RoutingSettings {
        let userDefaults = UserDefaults.standard
        let mode = userDefaults.string(forKey: "vpn_routing_mode") ?? "all"
        let customRoutes = userDefaults.string(forKey: "vpn_custom_routes") ?? ""

        return RoutingSettings(mode: mode, customRoutes: customRoutes)
    }

    /**
     * NAME: handleSetRoutingSettings
     *
     * DESCRIPTION:
     *     Handles routing settings update, equivalent to POST /api/settings/routing.
     *
     * PARAMETERS:
     *     arguments - Routing settings
     *     result - Flutter result callback
     */
    private func handleSetRoutingSettings(_ arguments: Any?, result: @escaping FlutterResult) async {
        guard let args = arguments as? [String: Any] else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Invalid routing settings",
                details: nil
            ))
            return
        }

        logger.debug("Processing set routing settings request")

        do {
            // Extract routing settings from arguments
            let mode = args["mode"] as? String ?? "all"
            let customRoutes = args["custom_routes"] as? String ?? ""

            // Store routing settings for use during VPN connection
            await storeRoutingSettings(mode: mode, customRoutes: customRoutes)

            logger.info("Routing settings stored successfully", metadata: [
                "mode": mode,
                "custom_routes": customRoutes
            ])

            // Return response
            let response: [String: Any] = [
                "success": true,
                "message": "Routing settings stored successfully"
            ]

            result(response)
            logger.info("Routing settings update completed")

        } catch let error as VPNServiceError {
            let flutterError = convertVPNErrorToFlutter(error)
            result(flutterError)
            logger.error("Routing settings update failed", metadata: ["error": error.localizedDescription])
        } catch {
            result(FlutterError(
                code: "SETTINGS_UPDATE_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Routing settings update failed with unexpected error", metadata: [
                "error": error.localizedDescription
            ])
        }
    }

    /**
     * NAME: handleSetServerProviderUrl
     *
     * DESCRIPTION:
     *     Handles set server provider URL request.
     *     Updates the server list URL and fetches new server list.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing URL
     *     result - Flutter result callback
     */
    private func handleSetServerProviderUrl(_ arguments: Any?, result: @escaping FlutterResult) async {
        guard let args = arguments as? [String: Any],
              let url = args["url"] as? String else {
            result(FlutterError(
                code: "INVALID_ARGUMENTS",
                message: "Missing or invalid URL parameter",
                details: nil
            ))
            return
        }

        logger.debug("Processing set server provider URL request", metadata: ["url": url])

        do {
            // Ensure VPN service is initialized
            guard let vpnService = vpnService else {
                result(FlutterError(
                    code: "VPN_SERVICE_NOT_AVAILABLE",
                    message: "VPN service is not initialized",
                    details: nil
                ))
                return
            }

            // Fetch server list from the new URL
            guard let requestUrl = URL(string: url) else {
                result(FlutterError(
                    code: "INVALID_URL",
                    message: "Invalid server list URL format",
                    details: nil
                ))
                return
            }

            var request = URLRequest(url: requestUrl)
            request.timeoutInterval = 10.0
            request.setValue("application/json", forHTTPHeaderField: "Accept")
            request.setValue("*/*", forHTTPHeaderField: "User-Agent")
            request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")

            // Create URL session for HTTP requests
            let config = URLSessionConfiguration.default
            config.timeoutIntervalForRequest = 10.0
            config.timeoutIntervalForResource = 30.0

            let session = URLSession(configuration: config)

            logger.info("Fetching server list from new URL", metadata: ["url": url])

            // Fetch server list
            let (data, response) = try await session.data(for: request)

            // Validate HTTP response
            if let httpResponse = response as? HTTPURLResponse {
                guard httpResponse.statusCode == 200 else {
                    result(FlutterError(
                        code: "HTTP_ERROR",
                        message: "Server returned status code \(httpResponse.statusCode)",
                        details: nil
                    ))
                    return
                }
            }

            logger.info("Server list fetched successfully, parsing JSON")

            // Parse JSON response
            let serverListWithVersion = try parseServerListJSON(data)

            logger.info("Parsed \(serverListWithVersion.servers.count) servers, updating VPN service")

            // Update server list in VPN service with version checking
            try await vpnService.serverService.updateServerListWithVersion(serverListWithVersion.servers, version: serverListWithVersion.version)

            // Return success response
            let successResponse: [String: Any] = [
                "success": true,
                "message": "Server provider URL updated successfully"
            ]

            result(successResponse)
            logger.info("Server provider URL update completed", metadata: [
                "url": url,
                "server_count": "\(serverListWithVersion.servers.count)"
            ])

        } catch {
            result(FlutterError(
                code: "SERVER_UPDATE_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Server provider URL update failed", metadata: [
                "url": url,
                "error": error.localizedDescription
            ])
        }
    }



    /**
     * NAME: handleHealthCheck
     *
     * DESCRIPTION:
     *     Handles health check request, equivalent to GET /api/health.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleHealthCheck(result: @escaping FlutterResult) async {
        logger.debug("Processing health check request")

        // Get health information
        let vpnState = await vpnService?.getConnectionState() ?? .disconnected
        let healthInfo: [String: Any] = [
            "status": "healthy",
            "version": getAppVersion(),
            "vpn_status": "\(vpnState.rawValue)"
        ]

        let response: [String: Any] = [
            "success": true,
            "data": healthInfo
        ]

        result(response)
        logger.debug("Health check completed")
    }

    // MARK: - Error Conversion

    /**
     * NAME: convertVPNErrorToFlutter
     *
     * DESCRIPTION:
     *     Converts VPNServiceError to FlutterError with unified error codes.
     *     Uses VPNServiceError.errorCode and errorType for consistency.
     *
     * PARAMETERS:
     *     error - VPN service error
     *
     * RETURNS:
     *     FlutterError - Converted Flutter error
     */
    private func convertVPNErrorToFlutter(_ error: VPNServiceError) -> FlutterError {
        return FlutterError(
            code: String(error.errorCode),
            message: error.localizedDescription,
            details: [
                "error_code": error.errorCode,
                "error_type": error.errorType
            ]
        )
    }

    // MARK: - Helper Methods

    /**
     * NAME: getAppVersion
     *
     * DESCRIPTION:
     *     Gets application version string.
     *
     * RETURNS:
     *     String - Application version
     */
    private func getAppVersion() -> String {
        #if os(iOS)
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
        #elseif os(macOS)
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
        #else
        return "unknown"
        #endif
    }
}

// MARK: - FlutterStreamHandler

extension PlatformChannelHandler: FlutterStreamHandler {

    /**
     * NAME: onListen
     *
     * DESCRIPTION:
     *     Called when Flutter starts listening to event stream.
     *     Equivalent to WebSocket connection establishment in Go backend.
     *
     * PARAMETERS:
     *     arguments - Stream arguments
     *     events - Event sink for sending events to Flutter
     *
     * RETURNS:
     *     FlutterError? - Error if stream setup fails
     */
    public func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        logger.info("Flutter event stream connected")

        // Send initial status (equivalent to Go WebSocket initial status)
        Task {
            await sendInitialStatus()
        }



        return nil
    }

    /**
     * NAME: onCancel
     *
     * DESCRIPTION:
     *     Called when Flutter stops listening to event stream.
     *     Equivalent to WebSocket disconnection in Go backend.
     *
     * PARAMETERS:
     *     arguments - Stream arguments
     *
     * RETURNS:
     *     FlutterError? - Error if stream cleanup fails
     */
    public func onCancel(withArguments arguments: Any?) -> FlutterError? {


        // STEP 4: Reset throttling timestamps when disconnected
        lastTrafficEventTime = Date.distantPast
        lastHeartbeatEventTime = Date.distantPast

        self.eventSink = nil
        logger.info("Flutter event stream disconnected - throttling timestamps reset")
        return nil
    }

    /**
     * NAME: sendInitialStatus
     *
     * DESCRIPTION:
     *     Sends initial status and server list to Flutter.
     *     Equivalent to Go WebSocket initial data push.
     *     STEP 3: Enhanced to also send current connection server info.
     */
    private func sendInitialStatus() async {
        // Send current status
        do {
            if let status = try await vpnService?.getConnectionInfo() {
                // Include network interface information in initial status if connected
                let includeNetworkInterface = (status.state == .connected)
                let statusData = status.toDictionary(includeNetworkInterface: includeNetworkInterface, vpnService: vpnService)

                sendEvent(type: "status", data: statusData)

                // STEP 3: Also send current connection server info if connected
                if status.state == .connected, let connectedServer = status.connectedServer {
                    await pushConnectionServerEvent(server: connectedServer)
                    logger.debug("Initial connection server info sent to Flutter")
                }
            }

            // Send server list
            let servers = try await vpnService?.serverService.getServerList() ?? []
            let serverDicts = servers.map { $0.toDictionary() }
            sendEvent(type: "servers", data: serverDicts)

            logger.debug("Initial status sent to Flutter")
        } catch {
            logger.error("Failed to send initial status", metadata: ["error": error.localizedDescription])
        }
    }

    /**
     * NAME: sendEvent
     *
     * DESCRIPTION:
     *     Sends event to Flutter through event channel.
     *     Equivalent to Go WebSocket event broadcasting.
     *     STEP 4: Enhanced with frequency control and connection checking.
     *
     * PARAMETERS:
     *     type - Event type (matching Go WebSocket event types)
     *     data - Event data
     */
    private func sendEvent(type: String, data: Any) {
        // STEP 4: Check if event pushing is enabled
        guard eventPushingEnabled else {
            logger.debug("Event pushing disabled, event discarded", metadata: ["type": type])
            return
        }

        guard let eventSink = eventSink else {
            logger.debug("No event sink available, skipping event", metadata: ["type": type])
            return
        }

        // STEP 4: Apply frequency throttling for specific event types
        let now = Date()
        switch type {
        case "traffic":
            guard now.timeIntervalSince(lastTrafficEventTime) >= trafficEventThrottle else {
                logger.debug("Traffic event throttled", metadata: ["throttle_seconds": "\(trafficEventThrottle)"])
                return
            }
            lastTrafficEventTime = now

        case "heartbeat":
            guard now.timeIntervalSince(lastHeartbeatEventTime) >= heartbeatEventThrottle else {
                logger.debug("Heartbeat event throttled", metadata: ["throttle_seconds": "\(heartbeatEventThrottle)"])
                return
            }
            lastHeartbeatEventTime = now



        default:
            // No throttling for other event types (status, conn_server, servers, ping_*, error)
            break
        }

        let event: [String: Any] = [
            "event": type,
            "data": data
        ]

        // Add debug logging for status events with timestamp
        // if type == "status" {
        //     if let dataDict = data as? [String: Any], let status = dataDict["status"] as? String {
        //         let timestamp = Date().timeIntervalSince1970 * 1000
        //         NSLog("🚨 CONN_FAIL_DEBUG: iOS sending status event - status: %@, timestamp: %.0f", status, timestamp)
        //         print("🚨 CONN_FAIL_DEBUG: iOS sending status event - status: \(status), timestamp: \(Int(timestamp))")

        //         // Force flush to ensure output appears
        //         fflush(stdout)
        //     }
        // }

        DispatchQueue.main.async {
            eventSink(event)
        }

        // logger.debug("Event sent to Flutter", metadata: [
        //     "type": type,
        //     "throttled": "false"
        // ])
    }
}

// MARK: - VPNServiceDelegate

extension PlatformChannelHandler: VPNServiceDelegate {

    /**
     * NAME: vpnService(_:didChangeState:)
     *
     * DESCRIPTION:
     *     Called when VPN connection state changes.
     *     Equivalent to Go WebSocket status event broadcasting.
     *     STEP 3: Enhanced to push conn_server event when connected.
     */
    public func vpnService(_ service: VPNService, didChangeState state: ConnectionState) {
        let timestamp = Date().timeIntervalSince1970 * 1000
        // NSLog("🚨 CONN_FAIL_DEBUG: VPN state changed delegate called - state: %d, timestamp: %.0f", state.rawValue, timestamp) // Debug NSLog commented for production
        // print("🚨 CONN_FAIL_DEBUG: VPN state changed delegate called - state: \(state.rawValue), timestamp: \(Int(timestamp))") // Debug print commented for production
        // fflush(stdout) // Debug flush commented for production
        logger.info("VPN state changed delegate called", metadata: ["state": "\(state.rawValue)"])

        // Add detailed debug logging for disconnected states during connection
        if state == .disconnected {
            // NSLog("🔍 DEBUG: Disconnected state received - checking VPN service current state") // Debug NSLog commented for production
            // print("🔍 DEBUG: Disconnected state received - checking VPN service current state") // Debug print commented for production
            Task {
                let currentVPNState = await service.getCurrentVPNState()
                // NSLog("🔍 DEBUG: Current VPN service state when disconnected received: %@", "\(currentVPNState)") // Debug NSLog commented for production
                // print("🔍 DEBUG: Current VPN service state when disconnected received: \(currentVPNState)") // Debug print commented for production
            }
        }

        // Convert state to status info first
        Task {
            do {
                logger.info("TRACE: Getting connection info for status event")
                let status = try await service.getConnectionInfo()

                // State deduplication: compare the actual status state that will be sent
                if let lastState = lastSentState, lastState == status.state {
                    // NSLog("🚨 CONN_FAIL_DEBUG: Duplicate state change ignored - status: %d (same as last sent)", status.state.rawValue) // Debug NSLog commented for production
                    // print("🚨 CONN_FAIL_DEBUG: Duplicate state change ignored - status: \(status.state.rawValue) (same as last sent)") // Debug print commented for production
                    return
                }

                // Update deduplication tracking with the actual status state
                lastSentState = status.state
                lastStateChangeTime = Date()

                // NSLog("🚨 CONN_FAIL_DEBUG: About to send status event - status: %d", status.state.rawValue) // Debug NSLog commented for production
                // print("🚨 CONN_FAIL_DEBUG: About to send status event - status: \(status.state.rawValue)") // Debug print commented for production
                logger.info("Sending status event to Flutter", metadata: ["status": "\(status.state.rawValue)"])

                // Include network interface information in status event for iOS platform
                let includeNetworkInterface = (status.state == .connected)
                let statusData = status.toDictionary(includeNetworkInterface: includeNetworkInterface, vpnService: vpnService)

                if includeNetworkInterface {
                    // print("🔍 [PlatformChannelHandler] Status event includes network interface information") // Debug print commented for production
                    logger.info("Status event includes network interface information", metadata: [
                        "interface_name": statusData["interface_name"] as? String ?? "",
                        "local_ip": statusData["local_ip"] as? String ?? "",
                        "itforce_ip": statusData["itforce_ip"] as? String ?? ""
                    ])
                }

                sendEvent(type: "status", data: statusData)

                // STEP 3: Push conn_server event when connected state is reached
                if state == .connected, let connectedServer = status.connectedServer {
                    await pushConnectionServerEvent(server: connectedServer)
                }
            } catch {
                logger.error("Failed to get connection info", metadata: ["error": error.localizedDescription])
            }
        }
    }

    /**
     * NAME: vpnService(_:didConnectToServer:)
     *
     * DESCRIPTION:
     *     Called when VPN connects to a server.
     *     Equivalent to Go WebSocket connection server event.
     *     STEP 3: Optimized to use centralized conn_server event pushing.
     */
    public func vpnService(_ service: VPNService, didConnectToServer server: ServerInfo) {
        logger.info("VPN connected to server", metadata: [
            "server_id": server.id,
            "server_name": server.name,
            "server_address": server.serverName,
            "server_port": "\(server.serverPort)"
        ])

        // STEP 3: Use centralized connection server event pushing
        Task {
            await pushConnectionServerEvent(server: server)
        }
    }

    /**
     * NAME: vpnService(_:didDisconnectWithReason:)
     *
     * DESCRIPTION:
     *     Called when VPN disconnects.
     *     Note: Status event is already sent by didChangeState, so we don't send duplicate status here.
     *     This method is kept for logging and potential future disconnect-specific handling.
     */
    public func vpnService(_ service: VPNService, didDisconnectWithReason reason: String?) {
        logger.info("VPN disconnected", metadata: ["reason": reason ?? "unknown"])

        // Note: Status event is already sent by didChangeState when state transitions to disconnected
        // No need to send duplicate status event here to avoid the duplicate notification issue
        logger.debug("Disconnect notification received - status event already sent by state change")
    }

    /**
     * NAME: vpnService(_:didUpdateTraffic:)
     *
     * DESCRIPTION:
     *     Called when traffic statistics are updated.
     *     Equivalent to Go WebSocket traffic event broadcasting.
     *     STEP 1 VERIFICATION: Enhanced logging to verify Event Channel connectivity.
     */
    public func vpnService(_ service: VPNService, didUpdateTraffic stats: TrafficStatistics) {
        logger.debug("VPN traffic statistics received from VPNService", metadata: [
            "upload_speed": "\(stats.uploadSpeed)",
            "download_speed": "\(stats.downloadSpeed)",
            "total_upload": "\(stats.totalUpload)",
            "total_download": "\(stats.totalDownload)"
        ])

        // Send traffic statistics (matching Go WebSocket traffic event format)
        let trafficData: [String: Any] = [
            "upload_speed": stats.uploadSpeed,
            "download_speed": stats.downloadSpeed,
            "total_upload": stats.totalUpload,
            "total_download": stats.totalDownload,
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]
        sendEvent(type: "traffic", data: trafficData)
        logger.debug("Traffic event sent to Flutter Event Channel", metadata: [
            "event_type": "traffic",
            "has_event_sink": "\(eventSink != nil)"
        ])
    }

    /**
     * NAME: vpnService(_:didEncounterError:)
     *
     * DESCRIPTION:
     *     Called when VPN service encounters an error.
     *     Equivalent to Go WebSocket error event broadcasting.
     */
    public func vpnService(_ service: VPNService, didEncounterError error: VPNServiceError) {
        logger.error("VPN service error", metadata: ["error": error.localizedDescription])

        // Convert error to event data (matching Go WebSocket error event format)
        let errorData: [String: Any] = [
            "code": getErrorCode(for: error),
            "message": error.localizedDescription,
            "type": getErrorType(for: error)
        ]

        sendEvent(type: "error", data: errorData)
    }

    /**
     * NAME: vpnService(_:didRequireNetworkExtensionRebuild:)
     *
     * DESCRIPTION:
     *     Called when NetworkExtension rebuild is required for server switch.
     *     iOS/macOS specific event not present in Go backend.
     */
    public func vpnService(_ service: VPNService, didRequireNetworkExtensionRebuild server: ServerInfo) {
        logger.info("NetworkExtension rebuild required", metadata: ["server": server.name])

        // Send iOS/macOS specific event for NE rebuild
        let rebuildData: [String: Any] = [
            "server": server.toDictionary(),
            "reason": "server_switch",
            "estimated_time": 3000 // 3 seconds estimated rebuild time
        ]

        sendEvent(type: "ne_rebuild_required", data: rebuildData)
    }

    /**
     * NAME: vpnService(_:didUpdateServerList:)
     *
     * DESCRIPTION:
     *     Called when server list is updated.
     *     Equivalent to Go WebSocket server list event broadcasting.
     */
    public func vpnService(_ service: VPNService, didUpdateServerList servers: [ServerInfo]) {
        logger.info("Server list updated", metadata: ["server_count": "\(servers.count)"])

        // Send server list update
        let serverDicts = servers.map { $0.toDictionary() }
        sendEvent(type: "servers", data: serverDicts)
    }

    /**
     * NAME: vpnService(_:didCompletePing:)
     *
     * DESCRIPTION:
     *     Called when server ping operation completes.
     *     Equivalent to Go WebSocket ping results event broadcasting.
     */
    public func vpnService(_ service: VPNService, didCompletePing results: [String: PingResult]) {
        print("🔍 [PING_DEBUG] PlatformChannelHandler.didCompletePing() - DELEGATE CALLED - results count: \(results.count)")
        print("🔍 [PING_DEBUG] didCompletePing method entered - thread: \(Thread.current)")
        logger.info("Ping completion delegate called")
        logger.info("didCompletePing method entered")

        // Log individual ping results for debugging
        for (serverID, result) in results {
            print("🔍 [PING_DEBUG] Ping result - server: \(serverID), latency: \(result.latency)ms, success: \(result.isSuccess)")
        }
        logger.info("Ping operation completed", metadata: ["tested_servers": "\(results.count)"])

        // Add NSLog for system-level logging
        // NSLog("🔥🔥🔥 [PlatformChannelHandler] NSLog: didCompletePing called with %d results", results.count) // Debug NSLog commented for production

        // Log ping results details
        for (serverID, result) in results {
            logger.info("Ping result", metadata: ["server_id": serverID, "latency": "\(result.latency)ms", "success": "\(result.isSuccess)"])
        }

        // Get fresh server data directly from ServerManager (bypasses ServerService cache)
        Task {
            do {
                logger.info("Getting fresh server list from ServerService")
                let servers = try await service.serverService.getFreshServerList()
                logger.info("Got servers with fresh ping data", metadata: ["count": "\(servers.count)"])

                // Log some server ping values for verification
                let serversWithPing = servers.filter { $0.ping > 0 }
                logger.info("Servers with valid ping", metadata: ["valid_count": "\(serversWithPing.count)", "total_count": "\(servers.count)"])
                for server in serversWithPing.prefix(3) {
                    // logger.debug("Server \(server.name): \(server.ping)ms") // Debug log commented for production
                }

                let serverDicts = servers.map { $0.toDictionary() }
                logger.info("Converted servers to dictionaries", metadata: ["count": "\(serverDicts.count)"])
                print("🔍 [PING_DEBUG] Converted \(serverDicts.count) servers to dictionaries")

                // Send ping results event (equivalent to Go backend ping_results)
                logger.info("Sending ping_results event", metadata: ["server_count": "\(serverDicts.count)"])
                print("🔍 [PING_DEBUG] About to send ping_results event with \(serverDicts.count) servers")
                sendEvent(type: "ping_results", data: ["servers": serverDicts])
                print("🔍 [PING_DEBUG] ping_results event sent successfully")

                // Send ping complete event
                logger.info("Sending ping_complete event")
                print("🔍 [PING_DEBUG] About to send ping_complete event")
                sendEvent(type: "ping_complete", data: [:])
                print("🔍 [PING_DEBUG] ping_complete event sent successfully")

            } catch {
                logger.error("Failed to get fresh server list for ping results", metadata: ["error": error.localizedDescription])
            }
        }
    }

    // MARK: - New VPN Extension Event Delegate Methods

    /**
     * NAME: vpnService(_:didUpdateNetworkInterface:ipAddress:)
     *
     * DESCRIPTION:
     *     Called when network interface information is updated.
     *     Sends interface update event to Flutter UI.
     */
    public func vpnService(_ service: VPNService, didUpdateNetworkInterface interfaceName: String, ipAddress: String) {
        logger.info("Network interface updated", metadata: [
            "interface_name": interfaceName,
            "ip_address": ipAddress
        ])

        // 获取当前tunnel IP
        let tunnelIP = service.getTunnelIPAddress()

        // 使用interface_info事件类型以复用Flutter端现有的处理逻辑
        var interfaceData: [String: Any] = [
            "interface_name": interfaceName,
            "local_ip": ipAddress,
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]

        // 只有当tunnel IP有效时才包含tun_ip字段，避免覆盖Flutter端已有的有效cloud ip
        if let tunnelIP = tunnelIP, !tunnelIP.isEmpty {
            interfaceData["tun_ip"] = tunnelIP
            logger.info("Including tunnel IP in interface update", metadata: [
                "tunnel_ip": tunnelIP
            ])
        } else {
            logger.info("Tunnel IP not available, excluding tun_ip field to preserve existing cloud ip")
        }

        sendEvent(type: "interface_info", data: interfaceData)
    }

    /**
     * NAME: vpnService(_:didReconnectSuccessfully:serverAddress:)
     *
     * DESCRIPTION:
     *     Called when VPN reconnection succeeds.
     *     Sends status event with reconnection success message to Flutter UI.
     */
    public func vpnService(_ service: VPNService, didReconnectSuccessfully tunnelIP: String, serverAddress: String) {
        logger.info("VPN reconnection successful", metadata: [
            "tunnel_ip": tunnelIP,
            "server_address": serverAddress
        ])

        // 复用status事件类型，通过message区分重连成功事件
        let statusData: [String: Any] = [
            "status": "connected",
            "message": "reconnection_success",
            "tunnel_ip": tunnelIP,
            "server_address": serverAddress,
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]

        sendEvent(type: "status", data: statusData)
    }

    /**
     * NAME: vpnService(_:didFailToConnect:serverAddress:)
     *
     * DESCRIPTION:
     *     Called when VPN connection fails.
     *     Sends status event with connection failure message to Flutter UI.
     */
    public func vpnService(_ service: VPNService, didFailToConnect errorMessage: String, serverAddress: String) {
        logger.warning("VPN connection failed", metadata: [
            "error_message": errorMessage,
            "server_address": serverAddress
        ])

        // 复用status事件类型，通过message区分连接失败事件
        let statusData: [String: Any] = [
            "status": "disconnected",
            "message": "connection_failure: \(errorMessage)",
            "server_address": serverAddress,
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]

        sendEvent(type: "status", data: statusData)
    }

    /**
     * NAME: vpnService(_:didFailToReconnect:serverAddress:)
     *
     * DESCRIPTION:
     *     Called when VPN reconnection fails.
     *     Sends status event with reconnection failure message to Flutter UI.
     */
    public func vpnService(_ service: VPNService, didFailToReconnect errorMessage: String, serverAddress: String) {
        logger.warning("VPN reconnection failed", metadata: [
            "error_message": errorMessage,
            "server_address": serverAddress
        ])

        // 复用status事件类型，通过message区分重连失败事件
        let statusData: [String: Any] = [
            "status": "disconnected",
            "message": "reconnection_failure: \(errorMessage)",
            "server_address": serverAddress,
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]

        sendEvent(type: "status", data: statusData)
    }

    /**
     * NOTE: vpnService(_:didReceiveHeartbeat:) method removed
     *
     * Heartbeat is now handled entirely by VPN extension to prevent blocking
     * when main app is backgrounded. Flutter UI can get connection status
     * through other means if needed.
     */

    /**
     * NAME: vpnService(_:didUpdateNetworkInterface:interfaceType:ipAddress:)
     *
     * DESCRIPTION:
     *     Called when network interface changes during reconnection.
     *     Updates UI statistics interface information.
     */
    public func vpnService(_ service: VPNService, didUpdateNetworkInterface interfaceName: String, interfaceType: String, ipAddress: String) {
        logger.info("Network interface updated", metadata: [
            "interface_name": interfaceName,
            "interface_type": interfaceType,
            "ip_address": ipAddress
        ])

        logger.info("Sending interface update to Flutter UI", metadata: [
            "interface_name": interfaceName,
            "local_ip": ipAddress,
            "note": "tun_ip_not_included_for_interface_change"
        ])

        // Send interface info event to Flutter UI - only include interface and local IP
        // Don't include tun_ip field so Flutter UI won't update that field
        let interfaceData: [String: Any] = [
            "interface_name": interfaceName,
            "local_ip": ipAddress,
            "timestamp": Int64(Date().timeIntervalSince1970) // seconds (matching InterfaceInfo.fromJson)
        ]

        sendEvent(type: "interface_info", data: interfaceData)
        logger.debug("Interface info event sent to Flutter Event Channel", metadata: [
            "event_type": "interface_info",
            "interface_name": interfaceName,
            "fields_included": "interface_name,local_ip,timestamp",
            "tun_ip_included": "false"
        ])
    }

    // MARK: - Connection Server Event Helper (STEP 3)

    /**
     * NAME: pushConnectionServerEvent
     *
     * DESCRIPTION:
     *     Pushes conn_server event with optimized format matching Go backend.
     *     STEP 3: Centralized conn_server event pushing for consistency.
     *
     * PARAMETERS:
     *     server - Server information to push
     */
    private func pushConnectionServerEvent(server: ServerInfo) async {
        guard eventSink != nil else {
            return // No event sink available
        }

        logger.info("Pushing connection server event", metadata: [
            "server_id": server.id,
            "server_name": server.name,
            "server_address": server.serverName
        ])

        // STEP 3: Optimized conn_server event data format (exactly matching Go backend ConnServerData)
        let connectionData: [String: Any] = [
            "server": [
                "id": server.id,
                "name": server.name,
                "name_en": server.nameEn,
                "server_name": server.serverName,
                "server_port": server.serverPort,
                "ping": server.ping,
                "isauto": server.isAuto,
                "status": server.status.rawValue,
                "isdefault": false // ServerInfo doesn't have isDefault property, use false as default
            ],
            "timestamp": Int64(Date().timeIntervalSince1970)
        ]

        sendEvent(type: "conn_server", data: connectionData)
        logger.info("Connection server event pushed to Flutter Event Channel", metadata: [
            "event_type": "conn_server",
            "server_id": server.id,
            "timestamp": "\(connectionData["timestamp"] ?? 0)"
        ])
    }

    // MARK: - Event Channel Connection Monitoring (STEP 4)

    /**
     * NAME: isEventChannelConnected
     *
     * DESCRIPTION:
     *     Checks if Event Channel is currently connected.
     *     STEP 4: Added for connection status monitoring.
     *
     * RETURNS:
     *     Bool - Whether Event Channel is connected
     */
    public func isEventChannelConnected() -> Bool {
        return eventSink != nil
    }

    /**
     * NAME: getEventPushingStatistics
     *
     * DESCRIPTION:
     *     Returns statistics about event pushing performance.
     *     STEP 4: Added for performance monitoring.
     *
     * RETURNS:
     *     Dictionary with event pushing statistics
     */
    public func getEventPushingStatistics() -> [String: Any] {
        let now = Date()
        return [
            "event_pushing_enabled": eventPushingEnabled,
            "event_channel_connected": isEventChannelConnected(),
            "last_traffic_event_seconds_ago": now.timeIntervalSince(lastTrafficEventTime),
            "last_heartbeat_event_seconds_ago": now.timeIntervalSince(lastHeartbeatEventTime),
            "traffic_throttle_seconds": trafficEventThrottle,
            "heartbeat_throttle_seconds": heartbeatEventThrottle
        ]
    }

    /**
     * NAME: resetEventThrottling
     *
     * DESCRIPTION:
     *     Resets all event throttling timestamps.
     *     STEP 4: Added for manual throttling reset.
     */
    public func resetEventThrottling() {
        lastTrafficEventTime = Date.distantPast
        lastHeartbeatEventTime = Date.distantPast
        logger.info("Event throttling timestamps reset")
    }

    // MARK: - Error Helper Methods

    /**
     * NAME: getErrorCode
     *
     * DESCRIPTION:
     *     Gets numeric error code for VPN service error.
     *     Uses VPNServiceError.errorCode for consistency.
     *
     * PARAMETERS:
     *     error - VPN service error
     *
     * RETURNS:
     *     Int - Error code
     */
    private func getErrorCode(for error: VPNServiceError) -> Int {
        return error.errorCode
    }

    /**
     * NAME: getErrorType
     *
     * DESCRIPTION:
     *     Gets error type string for VPN service error.
     *     Uses VPNServiceError.errorType for consistency.
     *
     * PARAMETERS:
     *     error - VPN service error
     *
     * RETURNS:
     *     String - Error type
     */
    private func getErrorType(for error: VPNServiceError) -> String {
        return error.errorType
    }

    /**
     * NAME: selectBestAutoServer
     *
     * DESCRIPTION:
     *     Selects best auto server based on latency, matching Go backend SelectBestAutoServer() logic.
     *     Prioritizes online servers first, then sorts by latency.
     *
     * PARAMETERS:
     *     servers - Array of available servers
     *
     * RETURNS:
     *     ServerInfo? - Best auto server or nil if none available
     */
    private func selectBestAutoServer(from servers: [ServerInfo]) -> ServerInfo? {
        logger.info("Selecting best auto server using Go backend logic")

        // Filter automatic servers - consider all automatic servers
        let autoServers = servers.filter { $0.isAuto }

        guard !autoServers.isEmpty else {
            logger.info("No auto servers found")
            return nil
        }

        // Sort by latency - prioritize online servers first, then sort by latency
        // Ping failed servers (latency 0) are placed at the end
        let sortedServers = autoServers.sorted { server1, server2 in
            // Prioritize online servers
            if server1.status == .online && server2.status != .online {
                return true
            }
            if server1.status != .online && server2.status == .online {
                return false
            }

            // If status is the same, sort by latency
            // Handle ping failed servers (latency 0) - place them at the end
            if server1.ping == 0 && server2.ping > 0 {
                return false // server1 (ping failed) goes after server2
            }
            if server1.ping > 0 && server2.ping == 0 {
                return true // server1 goes before server2 (ping failed)
            }
            if server1.ping == 0 && server2.ping == 0 {
                return false // Both ping failed, maintain original order
            }

            // Both have valid ping values, sort by latency (low to high)
            return server1.ping < server2.ping
        }

        // Select the best server (first in sorted list)
        let bestServer = sortedServers[0]

        return bestServer
    }

    // MARK: - VPN Authorization Methods

    /**
     * NAME: handleCheckVPNPermission
     *
     * DESCRIPTION:
     *     Handles VPN permission status check for iOS.
     *     iOS handles VPN permissions automatically during connection,
     *     so this always returns granted for compatibility.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleCheckVPNPermission(result: @escaping FlutterResult) async {
        logger.info("Processing VPN permission check request (iOS - auto-granted)")

        // iOS平台权限在连接时自动处理，返回已授权状态以保持兼容性
        let response: [String: Any] = [
            "granted": true,
            "status": "authorized",
            "message": "iOS平台VPN权限在连接时自动处理"
        ]

        result(response)
        logger.info("VPN permission check completed (iOS - auto-granted)")
    }

    /**
     * NAME: handleRequestVPNPermission
     *
     * DESCRIPTION:
     *     Handles VPN permission request for iOS.
     *     iOS handles VPN permissions automatically during connection,
     *     so this always returns granted for compatibility.
     *
     * PARAMETERS:
     *     arguments - Request arguments (ignored for iOS)
     *     result - Flutter result callback
     */
    private func handleRequestVPNPermission(_ arguments: Any?, result: @escaping FlutterResult) async {
        logger.info("Processing VPN permission request (iOS - auto-granted)")

        // iOS平台权限在连接时自动处理，返回已授权状态以保持兼容性
        let response: [String: Any] = [
            "granted": true,
            "status": "authorized",
            "message": "iOS平台VPN权限在连接时自动处理"
        ]

        result(response)
        logger.info("VPN permission request completed (iOS - auto-granted)")
    }

    /**
     * NAME: handleLoadVPNConfiguration
     *
     * DESCRIPTION:
     *     Handles loading existing VPN configuration from system preferences.
     *     Should be called on app startup to restore VPN state.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleLoadVPNConfiguration(result: @escaping FlutterResult) async {
        logger.info("Processing load VPN configuration request")

        do {
            guard let vpnService = vpnService else {
                result(FlutterError(
                    code: "VPN_SERVICE_NOT_AVAILABLE",
                    message: "VPN service is not initialized",
                    details: nil
                ))
                return
            }

            // Load existing VPN configuration
            let configurationLoaded = await vpnService.loadExistingVPNConfiguration()

            let response: [String: Any] = [
                "success": configurationLoaded,
                "message": configurationLoaded ? "VPN configuration loaded successfully" : "No existing VPN configuration found"
            ]

            result(response)
            logger.info("Load VPN configuration completed", metadata: [
                "configuration_found": "\(configurationLoaded)"
            ])

        } catch {
            result(FlutterError(
                code: "LOAD_CONFIGURATION_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Load VPN configuration failed", metadata: [
                "error": error.localizedDescription
            ])
        }
    }

    /**
     * NAME: handleGetVPNStatus
     *
     * DESCRIPTION:
     *     Handles VPN status request.
     *     Returns current VPN connection status from VPN Manager.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private func handleGetVPNStatus(result: @escaping FlutterResult) async {
        logger.info("Processing get VPN status request")

        do {
            guard let vpnService = vpnService else {
                result(FlutterError(
                    code: "VPN_SERVICE_NOT_AVAILABLE",
                    message: "VPN service is not initialized",
                    details: nil
                ))
                return
            }

            // Get VPN connection status
            let vpnStatus = await vpnService.getVPNConnectionStatus()

            let response: [String: Any] = [
                "status": vpnStatus.rawValue,
                "status_description": getVPNStatusDescription(vpnStatus),
                "is_connected": vpnStatus == .connected
            ]

            result(response)
            logger.info("Get VPN status completed", metadata: [
                "status": "\(vpnStatus.rawValue)",
                "is_connected": "\(vpnStatus == .connected)"
            ])

        } catch {
            result(FlutterError(
                code: "GET_VPN_STATUS_FAILED",
                message: error.localizedDescription,
                details: nil
            ))
            logger.error("Get VPN status failed", metadata: [
                "error": error.localizedDescription
            ])
        }
    }

    /**
     * NAME: getVPNStatusDescription
     *
     * DESCRIPTION:
     *     Gets human-readable description for VPN status.
     *
     * PARAMETERS:
     *     status - VPN status
     *
     * RETURNS:
     *     String - Status description
     */
    private func getVPNStatusDescription(_ status: NEVPNStatus) -> String {
        switch status {
        case .invalid:
            return "Invalid"
        case .disconnected:
            return "Disconnected"
        case .connecting:
            return "Connecting"
        case .connected:
            return "Connected"
        case .reasserting:
            return "Reasserting"
        case .disconnecting:
            return "Disconnecting"
        @unknown default:
            return "Unknown"
        }
    }
}